#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 完全模拟实际的工具调用环境
console.log('=== Environment Check ===');
console.log('process.cwd():', process.cwd());
console.log('__dirname equivalent:', path.dirname(new URL(import.meta.url).pathname));
console.log('Node.js version:', process.version);
console.log('Platform:', process.platform);

// 检查 LICENSE 文件的各种访问方式
const workingDir = process.cwd();
const licensePath = path.resolve(workingDir, 'LICENSE');

console.log('\n=== File Access Tests ===');
console.log('License path:', licensePath);

// Test 1: fs.existsSync
console.log('1. fs.existsSync:', fs.existsSync(licensePath));

// Test 2: fs.accessSync
try {
  fs.accessSync(licensePath, fs.constants.F_OK);
  console.log('2. fs.accessSync (F_OK): true');
} catch (error) {
  console.log('2. fs.accessSync (F_OK): false -', error.message);
}

// Test 3: fs.accessSync with read permission
try {
  fs.accessSync(licensePath, fs.constants.R_OK);
  console.log('3. fs.accessSync (R_OK): true');
} catch (error) {
  console.log('3. fs.accessSync (R_OK): false -', error.message);
}

// Test 4: fs.statSync
try {
  const stats = fs.statSync(licensePath);
  console.log('4. fs.statSync: success -', {
    isFile: stats.isFile(),
    isDirectory: stats.isDirectory(),
    size: stats.size,
    mode: stats.mode.toString(8),
    uid: stats.uid,
    gid: stats.gid
  });
} catch (error) {
  console.log('4. fs.statSync: failed -', error.message);
}

// Test 5: fs.promises.stat
try {
  const stats = await fs.promises.stat(licensePath);
  console.log('5. fs.promises.stat: success -', {
    isFile: stats.isFile(),
    isDirectory: stats.isDirectory(),
    size: stats.size
  });
} catch (error) {
  console.log('5. fs.promises.stat: failed -', error.message);
}

// Test 6: fs.promises.access
try {
  await fs.promises.access(licensePath, fs.constants.F_OK);
  console.log('6. fs.promises.access (F_OK): true');
} catch (error) {
  console.log('6. fs.promises.access (F_OK): false -', error.message);
}

// Test 7: 尝试读取文件内容
try {
  const content = await fs.promises.readFile(licensePath, 'utf8');
  console.log('7. fs.promises.readFile: success - length:', content.length);
} catch (error) {
  console.log('7. fs.promises.readFile: failed -', error.message);
}

// Test 8: 检查路径的每个部分
console.log('\n=== Path Component Tests ===');
const pathParts = licensePath.split(path.sep);
let currentPath = '';
for (let i = 0; i < pathParts.length; i++) {
  if (i === 0 && pathParts[i] === '') {
    currentPath = '/';
  } else if (i === 0) {
    currentPath = pathParts[i];
  } else {
    currentPath = path.join(currentPath, pathParts[i]);
  }
  
  try {
    const exists = fs.existsSync(currentPath);
    const stats = exists ? fs.statSync(currentPath) : null;
    console.log(`${currentPath}: exists=${exists}, isDir=${stats?.isDirectory()}, isFile=${stats?.isFile()}`);
  } catch (error) {
    console.log(`${currentPath}: error - ${error.message}`);
  }
}

// Test 9: 检查当前工作目录中的文件列表
console.log('\n=== Directory Listing ===');
try {
  const files = fs.readdirSync(workingDir);
  const licenseFiles = files.filter(f => f.toLowerCase().includes('license'));
  console.log('Files containing "license":', licenseFiles);
  
  // 检查确切的文件名
  const hasLicense = files.includes('LICENSE');
  console.log('Has exact "LICENSE" file:', hasLicense);
  
  if (hasLicense) {
    const fullPath = path.join(workingDir, 'LICENSE');
    console.log('Full path of LICENSE:', fullPath);
    console.log('Path comparison:', fullPath === licensePath);
  }
} catch (error) {
  console.log('Directory listing failed:', error.message);
}

// Test 10: 检查路径字符串的详细信息
console.log('\n=== Path String Analysis ===');
console.log('License path length:', licensePath.length);
console.log('License path bytes:', Buffer.from(licensePath, 'utf8'));
console.log('License path chars:', [...licensePath].map(c => `${c}(${c.charCodeAt(0)})`));

// Test 11: 尝试不同的路径构造方式
console.log('\n=== Alternative Path Construction ===');
const altPath1 = path.join(workingDir, 'LICENSE');
const altPath2 = workingDir + path.sep + 'LICENSE';
const altPath3 = path.normalize(workingDir + '/LICENSE');

console.log('Original path:', licensePath);
console.log('path.join:', altPath1);
console.log('string concat:', altPath2);
console.log('path.normalize:', altPath3);

console.log('All paths equal:', 
  licensePath === altPath1 && 
  altPath1 === altPath2 && 
  altPath2 === altPath3
);

[altPath1, altPath2, altPath3].forEach((p, i) => {
  console.log(`Alt path ${i+1} exists:`, fs.existsSync(p));
});