# list_directory 工具路径处理修复

## 问题描述

当用户调用 `node packages/lang/dist/cli.js --interactive --debug` 命令测试时，遇到调用 `list_directory` 工具时总是会触发传入路径不是绝对路径的提示错误。

## 问题原因

`list_directory` 工具（LSTool）要求传入的 `path` 参数必须是绝对路径，但 LLM 经常会生成相对路径（如 `./`、`./src` 等），导致工具验证失败。

## 解决方案

在 `CoreToolWrapper` 类中添加了针对 `list_directory` 工具的特殊处理逻辑：

### 修改位置
- 文件：`packages/lang/src/tools/toolRegistry.ts`
- 类：`CoreToolWrapper`
- 方法：`invoke()` 和 `_call()`

### 修改内容

```typescript
// Special handling for list_directory tool - convert relative paths to absolute
if (this.name === 'list_directory' && args && typeof args === 'object' && 'path' in args) {
    const pathValue = args.path;
    if (typeof pathValue === 'string' && !path.isAbsolute(pathValue)) {
        const workingDir = this.config.getWorkingDir();
        const absolutePath = path.resolve(workingDir, pathValue);
        args.path = absolutePath;
        logger.debug(`[CoreToolWrapper] Converted relative path '${pathValue}' to absolute path '${absolutePath}' for list_directory tool`);
    }
}
```

### 处理逻辑

1. **检测工具类型**：只对 `list_directory` 工具进行特殊处理
2. **检查路径参数**：确认参数对象中存在 `path` 字段
3. **判断路径类型**：使用 `path.isAbsolute()` 检查是否为绝对路径
4. **路径转换**：如果是相对路径，使用 `path.resolve(workingDir, pathValue)` 转换为绝对路径
5. **日志记录**：记录路径转换过程，便于调试

### 支持的场景

- ✅ 相对路径：`./`、`./src`、`../parent` 等
- ✅ 绝对路径：`/absolute/path` 等（不做修改）
- ✅ 其他工具：不影响其他工具的路径处理
- ✅ 双重兼容：同时在 `invoke()` 和 `_call()` 方法中实现

### 测试验证

路径转换逻辑已通过单元测试验证：

```javascript
// 测试用例
testPathConversion('list_directory', { path: './' }, workingDir);
// 结果：{ path: '/Users/<USER>/projs/github/gemini-cli/packages/lang' }

testPathConversion('list_directory', { path: './src' }, workingDir);
// 结果：{ path: '/Users/<USER>/projs/github/gemini-cli/packages/lang/src' }

testPathConversion('list_directory', { path: '/absolute/path' }, workingDir);
// 结果：{ path: '/absolute/path' } (不变)
```

## 预期效果

修复后，当 LLM 生成相对路径的 `list_directory` 工具调用时：

1. **之前**：工具验证失败，提示"Path must be absolute"
2. **现在**：自动将相对路径转换为绝对路径，工具正常执行

## 兼容性

- ✅ 向后兼容：不影响现有的绝对路径调用
- ✅ 工具隔离：只影响 `list_directory` 工具，不影响其他工具
- ✅ 方法兼容：同时支持 `invoke()` 和 `_call()` 方法调用

## 使用示例

```bash
# 测试命令
node packages/lang/dist/cli.js --interactive --debug

# 现在可以正常处理这些调用：
# - list_directory with path: "./"
# - list_directory with path: "./src"
# - list_directory with path: "../"
```
