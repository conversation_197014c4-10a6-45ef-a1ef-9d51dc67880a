/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

// Main exports for the LangChain-based Gemini CLI implementation

// Configuration
export { LangChainConfig } from './config/config.js';
export type { 
  LangChainContentGeneratorConfig, 
  AgentState,
  LangChainToolWrapper 
} from './types/index.js';

// Core components
export { LangChainContentGenerator } from './core/contentGenerator.js';
export { StateGraphAgent } from './core/stateGraphAgent.js';
export { SessionManager } from './core/sessionManager.js';
export type { SessionData } from './core/sessionManager.js';

// Model factory
export {
  createChatModel,
  createEmbeddings,
  getModelDisplayName,
  validateModelName,
  getDefaultModel,
} from './core/modelFactory.js';

// Tools
export { 
  CoreToolWrapper, 
  LangChainToolRegistry 
} from './tools/toolRegistry.js';

// Re-export important types from core
export type {
  AuthType,
  ApprovalMode,
} from './types/index.js';

// Re-export CLI integration
export {
  shouldUseLangChain,
  createCLIInstance,
  getLangChainSettings,
  setupLangChainEnvironment,
} from './cli-integration.js';

import { logger } from './utils/logger.js';

// Global session mapping for interactive mode
const interactiveSessionMap = new Map<string, string>();

/**
 * Create a complete LangChain-based Gemini CLI instance
 */
export async function createLangChainGeminiCLI(params: import('@google/gemini-cli-core').ConfigParameters & {
  baseURL?: string;
  authType?: import('./types/index.js').ExtendedAuthType;
}) {
  // Create configuration
  const { LangChainConfig } = await import('./config/config.js');
  const config = new LangChainConfig(params);
  await config.initialize();
  
  // Create session manager with targetDir
  const { SessionManager } = await import('./core/sessionManager.js');
  const sessionManager = new SessionManager(
    100, // maxSessions
    24 * 60 * 60 * 1000, // sessionTimeout (24 hours)
    true, // persistenceEnabled
    params.targetDir // targetDir for .haicode directory
  );
  
  // Create agent - StateGraphAgent
  const { StateGraphAgent } = await import('./core/stateGraphAgent.js');
  const agent = new StateGraphAgent(
    config,
    getCoreSystemPrompt(),
    { enablePersistence: true }
  );
  
  return {
    config,
    sessionManager,
    agent,
    
    // Convenience methods
    async processMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): Promise<string> {
      const id = sessionId || sessionManager.createSession(userMemory);
      const history = sessionManager.getConversationHistory(id);

      // Add user message to session before processing
      const { HumanMessage, AIMessage } = await import('@langchain/core/messages');
      sessionManager.addMessage(id, new HumanMessage(userMessage));

      // Get the initial message count to track new messages
      const initialMessageCount = history.length + 1; // +1 for the user message we just added

      // Process the message
      const response = await agent.processMessage(userMessage, id, userMemory, history);

      // Get the final state from the agent's graph to capture all intermediate messages
      const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

      // Extract new messages that were generated during processing (tool calls, tool results, etc.)
      if (finalState && finalState.values && finalState.values.messages) {
        const allMessages = finalState.values.messages;
        const newMessages = allMessages.slice(initialMessageCount);

        // Add all new messages to session (this includes tool calls and tool results)
        if (newMessages.length > 0) {
          sessionManager.updateSession(id, newMessages);
        }
      } else {
        // Fallback: just add the final AI response if we can't get the full state
        if (response.trim()) {
          sessionManager.addMessage(id, new AIMessage(response));
        }
      }

      return response;
    },
    
    async *streamMessage(
      userMessage: string,
      sessionId?: string,
      userMemory?: string
    ): AsyncGenerator<string> {
      let id: string;

      if (sessionId) {
        // Check if we have a mapped session for this interactive session
        if (interactiveSessionMap.has(sessionId)) {
          id = interactiveSessionMap.get(sessionId)!;
        } else {
          // Create a new session and map it to the interactive session ID
          id = sessionManager.createSession(userMemory);
          interactiveSessionMap.set(sessionId, id);
          logger.debug(`[CLI] Mapped interactive session ${sessionId} to actual session ${id}`);
        }
      } else {
        id = sessionManager.createSession(userMemory);
      }

      const history = sessionManager.getConversationHistory(id);

      // Add user message to session before processing
      sessionManager.addMessage(id, new (await import('@langchain/core/messages')).HumanMessage(userMessage));

      // Get the initial message count to track new messages
      const initialMessageCount = history.length + 1; // +1 for the user message we just added

      let responseContent = '';

      // Stream the response and collect it
      for await (const chunk of agent.streamMessage(userMessage, id, userMemory, history)) {
        responseContent += chunk;
        yield chunk;
      }

      // Get the final state from the agent's graph to capture all intermediate messages
      try {
        const finalState = await agent.getGraph().getState({ configurable: { thread_id: id } });

        // Extract new messages that were generated during processing (tool calls, tool results, etc.)
        if (finalState && finalState.values && finalState.values.messages) {
          const allMessages = finalState.values.messages;
          const newMessages = allMessages.slice(initialMessageCount);

          // Add all new messages to session (this includes tool calls and tool results)
          if (newMessages.length > 0) {
            sessionManager.updateSession(id, newMessages);
          }
        } else {
          // Fallback: just add the final AI response if we can't get the full state
          if (responseContent.trim()) {
            sessionManager.addMessage(id, new (await import('@langchain/core/messages')).AIMessage(responseContent));
          }
        }
      } catch (error) {
        logger.error('[CLI] Failed to get final state from graph:', error);
        // Fallback: just add the final AI response
        if (responseContent.trim()) {
          sessionManager.addMessage(id, new (await import('@langchain/core/messages')).AIMessage(responseContent));
        }
      }
    },
  };
}

/**
 * Get the core system prompt (should match the one in core package)
 */
function getCoreSystemPrompt(): string {
  return `You are an interactive CLI agent specializing in software engineering tasks. Your primary goal is to help users safely and efficiently, adhering strictly to the following instructions and utilizing your available tools.

# Core Mandates

- **Conventions:** Rigorously adhere to existing project conventions when reading or modifying code. Analyze surrounding code, tests, and configuration first.
- **Libraries/Frameworks:** NEVER assume a library/framework is available or appropriate. Verify its established usage within the project before employing it.
- **Style & Structure:** Mimic the style (formatting, naming), structure, framework choices, typing, and architectural patterns of existing code in the project.
- **Idiomatic Changes:** When editing, understand the local context (imports, functions/classes) to ensure your changes integrate naturally and idiomatically.
- **Comments:** Add code comments sparingly. Focus on *why* something is done, especially for complex logic, rather than *what* is done.
- **Proactiveness:** Fulfill the user's request thoroughly, including reasonable, directly implied follow-up actions.
- **Confirm Ambiguity/Expansion:** Do not take significant actions beyond the clear scope of the request without confirming with the user.

# Primary Workflows

## Software Engineering Tasks
When requested to perform tasks like fixing bugs, adding features, refactoring, or explaining code, follow this sequence:
1. **Understand:** Use search and file reading tools extensively to understand file structures, existing code patterns, and conventions.
2. **Plan:** Build a coherent plan for how you intend to resolve the user's task.
3. **Execute:** Implement the plan, strictly adhering to the project's established conventions.
4. **Verify:** If applicable, verify the changes using the project's testing procedures.

# Operational Guidelines

## Tone and Style (CLI Interaction)
- **Concise & Direct:** Adopt a professional, direct, and concise tone suitable for a CLI environment.
- **Minimal Output:** Aim for fewer than 3 lines of text output per response whenever practical.
- **Clarity over Brevity:** While conciseness is key, prioritize clarity for essential explanations.
- **No Chitchat:** Avoid conversational filler. Get straight to the action or answer.
- **Tools vs. Text:** Use tools for actions, text output only for communication.

## Security and Safety Rules
- **Explain Critical Commands:** Before executing potentially destructive commands, explain what they will do.
- **File System Safety:** Always validate file paths and consider the impact of file operations.
- **Network Operations:** Be cautious with network requests and external resources.

Use your available tools to gather information, modify files, search code, and execute safe commands as needed to complete the user's requests effectively.`;
}