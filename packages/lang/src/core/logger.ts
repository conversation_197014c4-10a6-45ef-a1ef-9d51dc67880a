/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import path from 'node:path';
import { promises as fs } from 'node:fs';
import { BaseMessage } from '@langchain/core/messages';
import { getProjectTempDir } from '../utils/paths.js';
import { logger as log } from '../utils/logger.js';
import { SESSIONS_DIR_NAME, LOG_FILE_NAME, SESSION_FILE_NAME } from '../config/constants.js';

export enum MessageSenderType {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export interface LogEntry {
  sessionId: string;
  messageId: number;
  timestamp: string;
  type: MessageSenderType;
  message: string;
  metadata?: Record<string, unknown>;
}

export class HaicodeLogger {
  private haicodeDir: string | undefined;
  private sessionDir: string | undefined;
  private sessionLogFilePath: string | undefined;
  private sessionMetaFilePath: string | undefined;
  private sessionId: string | undefined;
  private messageId = 0; // Instance-specific counter for the next messageId
  private initialized = false;
  private logs: LogEntry[] = []; // In-memory cache for current session logs

  constructor(sessionId: string) {
    this.sessionId = sessionId;
  }

  private async _readSessionLogFile(): Promise<LogEntry[]> {
    if (!this.sessionLogFilePath) {
      throw new Error('Session log file path not set during read attempt.');
    }
    try {
      const fileContent = await fs.readFile(this.sessionLogFilePath, 'utf-8');
      const parsedLogs = JSON.parse(fileContent);
      if (!Array.isArray(parsedLogs)) {
        log.debug(
          `Session log file at ${this.sessionLogFilePath} is not a valid JSON array. Starting with empty logs.`,
        );
        await this._backupCorruptedLogFile('malformed_array');
        return [];
      }
      return parsedLogs;
    } catch (error: unknown) {
      if (error instanceof Error && 'code' in error && error.code === 'ENOENT') {
        // File doesn't exist yet, which is fine - return empty array
        return [];
      }
      log.debug('Error reading session log file:', error);
      if (error instanceof SyntaxError) {
        await this._backupCorruptedLogFile('syntax_error');
        return [];
      }
      throw error;
    }
  }

  private async _backupCorruptedLogFile(reason: string): Promise<void> {
    if (!this.sessionLogFilePath) {
      return;
    }
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupPath = `${this.sessionLogFilePath}.backup.${reason}.${timestamp}`;
      await fs.copyFile(this.sessionLogFilePath, backupPath);
      log.debug(`Backed up corrupted session log file to: ${backupPath}`);
    } catch (backupError) {
      log.debug('Failed to backup corrupted session log file:', backupError);
    }
  }

  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.haicodeDir = getProjectTempDir(process.cwd());
    const sessionsDir = path.join(this.haicodeDir, SESSIONS_DIR_NAME);
    this.sessionDir = path.join(sessionsDir, this.sessionId!);
    this.sessionLogFilePath = path.join(this.sessionDir, LOG_FILE_NAME);
    this.sessionMetaFilePath = path.join(this.sessionDir, SESSION_FILE_NAME);

    try {
      // Create session directory structure
      await fs.mkdir(this.sessionDir, { recursive: true });

      // Check if session log file exists
      let fileExisted = true;
      try {
        await fs.access(this.sessionLogFilePath);
      } catch (_e) {
        fileExisted = false;
      }

      // Read existing session logs or create empty array
      this.logs = await this._readSessionLogFile();
      if (!fileExisted && this.logs.length === 0) {
        await fs.writeFile(this.sessionLogFilePath, '[]', 'utf-8');
      }

      // Set messageId based on existing logs for this session
      this.messageId =
        this.logs.length > 0
          ? Math.max(...this.logs.map((entry) => entry.messageId)) + 1
          : 0;

      this.initialized = true;
    } catch (err) {
      log.error('Failed to initialize haicode logger:', err);
      this.initialized = false;
    }
  }

  private async _updateSessionLogFile(
    entryToAppend: LogEntry,
  ): Promise<LogEntry | null> {
    if (!this.sessionLogFilePath) {
      log.debug('Session log file path not set. Cannot persist log entry.');
      throw new Error('Session log file path not set during update attempt.');
    }

    let currentLogsOnDisk: LogEntry[];
    try {
      currentLogsOnDisk = await this._readSessionLogFile();
    } catch (readError) {
      log.debug(
        'Critical error reading session log file before append:',
        readError,
      );
      throw readError;
    }

    // For session-specific logs, determine the correct messageId based on current session logs
    const nextMessageIdForSession =
      currentLogsOnDisk.length > 0
        ? Math.max(...currentLogsOnDisk.map((e) => e.messageId)) + 1
        : 0;

    const finalEntryToAppend: LogEntry = {
      ...entryToAppend,
      messageId: nextMessageIdForSession,
    };

    currentLogsOnDisk.push(finalEntryToAppend);

    try {
      await fs.writeFile(
        this.sessionLogFilePath,
        JSON.stringify(currentLogsOnDisk, null, 2),
        'utf-8',
      );

      // Update our in-memory cache with the new disk state
      this.logs = currentLogsOnDisk;

      // Update the instance messageId to the next available for this session
      this.messageId = nextMessageIdForSession + 1;

      return finalEntryToAppend;
    } catch (writeError) {
      log.debug('Error writing to session log file:', writeError);
      throw writeError;
    }
  }

  async log(message: string, type: MessageSenderType = MessageSenderType.USER, metadata?: Record<string, unknown>): Promise<void> {
    if (!this.initialized) {
      log.error(
        'Haicode logger not initialized. Cannot log message.',
      );
      return;
    }

    const entry: LogEntry = {
      sessionId: this.sessionId!,
      messageId: this.messageId,
      timestamp: new Date().toISOString(),
      type,
      message,
      metadata,
    };

    try {
      await this._updateSessionLogFile(entry);
    } catch (error) {
      log.error('Failed to log message:', error);
    }
  }

  private _checkpointPath(tag: string): string {
    if (!tag.length) {
      throw new Error('No checkpoint tag specified.');
    }
    if (!this.sessionDir) {
      throw new Error('Session directory path not set.');
    }
    // Sanitize tag to prevent directory traversal attacks (same as core Logger)
    tag = tag.replace(/[^a-zA-Z0-9-_]/g, '');
    if (!tag) {
      log.error('Sanitized tag is empty setting to "default".');
      tag = 'default';
    }
    return path.join(this.sessionDir, `checkpoint-${tag}.json`);
  }

  async saveCheckpoint(conversation: BaseMessage[], tag: string): Promise<void> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot save a checkpoint.',
      );
      return;
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      // Convert BaseMessage to serializable format compatible with core logger
      const serializedConversation = conversation.map(msg => ({
        role: msg.constructor.name === 'HumanMessage' ? 'user' : 'model',
        parts: [{ text: String(msg.content) }],
        // Keep additional data for compatibility
        _langchain_type: msg.constructor.name,
        _additional_kwargs: msg.additional_kwargs,
      }));
      await fs.writeFile(checkpointPath, JSON.stringify(serializedConversation, null, 2), 'utf-8');
      log.debug(`Saved checkpoint '${tag}' to ${checkpointPath}`);
    } catch (error) {
      log.error('Error writing to checkpoint file:', error);
    }
  }

  async loadCheckpoint(tag: string): Promise<BaseMessage[]> {
    if (!this.initialized) {
      log.error(
        'Logger not initialized or checkpoint file path not set. Cannot load checkpoint.',
      );
      return [];
    }
    const checkpointPath = this._checkpointPath(tag);
    try {
      const fileContent = await fs.readFile(checkpointPath, 'utf-8');
      const parsedContent = JSON.parse(fileContent);
      if (!Array.isArray(parsedContent)) {
        log.warning(
          `Checkpoint file at ${checkpointPath} is not a valid JSON array. Returning empty checkpoint.`,
        );
        return [];
      }
      
      // Convert back from core logger format to LangChain BaseMessage
      const { HumanMessage, AIMessage, ToolMessage } = await import('@langchain/core/messages');

      return parsedContent.map((msgData: Record<string, unknown>) => {
        // Safely extract content from different formats
        const parts = msgData.parts as Array<{ text?: string }> | undefined;
        const content = parts?.[0]?.text || String(msgData.content || '');
        const additionalKwargs = (msgData._additional_kwargs || msgData.additional_kwargs || {}) as Record<string, unknown>;

        // Determine message type based on role or langchain type
        if (msgData.role === 'user' || msgData._langchain_type === 'HumanMessage') {
          return new HumanMessage({ content, additional_kwargs: additionalKwargs });
        } else if (msgData._langchain_type === 'ToolMessage') {
          // Reconstruct ToolMessage
          return new ToolMessage({
            content,
            tool_call_id: String(msgData.tool_call_id || ''),
            additional_kwargs: additionalKwargs,
          });
        } else {
          // AIMessage - reconstruct with tool calls if present
          const aiMessage = new AIMessage({ content, additional_kwargs: additionalKwargs });
          if (msgData.tool_calls && Array.isArray(msgData.tool_calls)) {
            aiMessage.tool_calls = msgData.tool_calls as Array<{
              id: string;
              name: string;
              args: Record<string, unknown>;
            }>;
          }
          return aiMessage;
        }
      });
    } catch (error) {
      log.error(`Failed to read or parse checkpoint file ${checkpointPath}:`, error);
      const nodeError = error as NodeJS.ErrnoException;
      if (nodeError.code === 'ENOENT') {
        // File doesn't exist, which is fine. Return empty array.
        return [];
      }
      return [];
    }
  }

  async saveSessionMetadata(metadata: Record<string, unknown>): Promise<void> {
    if (!this.initialized || !this.sessionMetaFilePath) {
      log.error('Logger not initialized or session metadata file path not set.');
      return;
    }

    try {
      const sessionMetadata = {
        sessionId: this.sessionId,
        createdAt: new Date().toISOString(),
        lastActivity: new Date().toISOString(),
        ...metadata,
      };

      await fs.writeFile(
        this.sessionMetaFilePath,
        JSON.stringify(sessionMetadata, null, 2),
        'utf-8'
      );
      log.debug(`Saved session metadata to ${this.sessionMetaFilePath}`);
    } catch (error) {
      log.error('Error writing session metadata file:', error);
    }
  }

  getSessionLogs(): LogEntry[] {
    return [...this.logs]; // Return all logs since they're session-specific now
  }

  getAllLogs(): LogEntry[] {
    return [...this.logs]; // Same as getSessionLogs since logs are session-specific
  }
}