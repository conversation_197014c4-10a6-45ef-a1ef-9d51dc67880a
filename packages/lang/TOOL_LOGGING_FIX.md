# SessionManager工具调用记录修正

## 问题描述

之前的SessionManager没有将LLM调用工具以及工具调用结果记录到.haicode目录中。具体问题包括：

1. **只记录用户消息和最终AI响应**：在`processMessage`和`streamMessage`方法中，只记录了用户输入和最终的AI响应，但没有记录中间的工具调用过程。

2. **缺少工具调用消息记录**：AIMessage中的`tool_calls`信息没有被记录到日志中。

3. **缺少工具结果消息记录**：ToolMessage的内容和元数据没有被记录到日志中。

## 修正方案

### 1. 修正消息记录逻辑

在`packages/lang/src/index.ts`中修正了`processMessage`和`streamMessage`方法：

- **获取完整的StateGraph状态**：通过`agent.getGraph().getState()`获取执行过程中产生的所有消息
- **记录中间消息**：将StateGraph执行过程中产生的所有消息（包括工具调用和工具结果）都记录到SessionManager中

### 2. 增强SessionManager日志记录

在`packages/lang/src/core/sessionManager.ts`中增强了日志记录逻辑：

- **AIMessage工具调用处理**：检测AIMessage中的`tool_calls`，将工具调用信息记录到日志的`metadata`中
- **ToolMessage处理**：将ToolMessage的内容和`tool_call_id`记录到日志中，并标记为`tool_result`类型
- **增强日志内容**：为工具调用和工具结果添加可读的标识符

## 修正后的功能

### 消息类型记录

现在SessionManager能够正确记录以下消息类型到.haicode日志中：

1. **用户消息 (HumanMessage)**
   ```json
   {
     "type": "user",
     "message": "用户输入的内容"
   }
   ```

2. **AI工具调用消息 (AIMessage with tool_calls)**
   ```json
   {
     "type": "assistant",
     "message": "AI响应内容 [工具调用: tool_name({args})]",
     "metadata": {
       "tool_calls": [
         {
           "id": "call_123",
           "name": "tool_name",
           "args": { "param": "value" }
         }
       ]
     }
   }
   ```

3. **工具结果消息 (ToolMessage)**
   ```json
   {
     "type": "assistant",
     "message": "[工具结果] 工具执行结果内容",
     "metadata": {
       "tool_call_id": "call_123",
       "message_type": "tool_result"
     }
   }
   ```

4. **普通AI响应 (AIMessage)**
   ```json
   {
     "type": "assistant",
     "message": "AI的普通响应内容"
   }
   ```

### 集成流程

修正后的完整流程：

1. **用户发送消息** → 记录到SessionManager和.haicode
2. **StateGraph执行**：
   - AI决定调用工具 → 生成AIMessage with tool_calls → 记录到.haicode
   - 工具执行 → 生成ToolMessage → 记录到.haicode
   - AI处理工具结果 → 生成最终AIMessage → 记录到.haicode
3. **返回最终响应给用户**

## 测试验证

通过多个测试验证了修正的有效性：

1. **单元测试**：验证SessionManager能正确记录各种消息类型
2. **集成测试**：验证StateGraphAgent和SessionManager的完整集成
3. **日志验证**：确认.haicode目录中的日志文件包含完整的工具调用记录

## 使用示例

```typescript
// 创建CLI实例
const cli = await createLangChainGeminiCLI({
  model: 'gemini-1.5-flash',
  apiKey: process.env.GEMINI_API_KEY,
  targetDir: process.cwd(),
  debugMode: true
});

// 处理包含工具调用的消息
const response = await cli.processMessage(
  '请读取package.json文件的内容',
  sessionId
);

// 所有中间步骤（工具调用、工具结果）都会自动记录到.haicode中
```

## 注意事项

1. **异步日志记录**：日志记录是异步的，可能需要短暂等待才能看到完整的日志
2. **错误处理**：如果无法获取StateGraph状态，会回退到只记录最终响应
3. **性能影响**：获取完整状态可能会有轻微的性能开销，但对用户体验影响很小

## 结论

修正后的SessionManager现在能够完整记录LLM与工具交互的全过程，为调试、审计和分析提供了完整的日志记录功能。
