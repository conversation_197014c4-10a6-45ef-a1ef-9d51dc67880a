#!/usr/bin/env node

/**
 * 测试脚本：验证在debug模式下是否能打印LLM的完整输入
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 测试调试输出功能...\n');

// 设置测试环境变量
const env = {
  ...process.env,
  OPENAI_API_KEY: 'sk-cITab2RRLmsfbUH-pNJg',
  OPENAI_BASE_URL: 'http://*************/web/unauth/LLM_api_proxy/v1',
  HAI_CODE_MODEL: 'ht::saas-deepseek-v3'
};

// 启动CLI进程
const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
const child = spawn('node', [cliPath, '--interactive', '--debug'], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let hasSeenLLMInput = false;

// 监听输出
child.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  process.stdout.write(text);
  
  // 检查是否看到了LLM输入调试信息
  if (text.includes('=== LLM 完整输入 ===')) {
    hasSeenLLMInput = true;
    console.log('\n✅ 检测到LLM完整输入调试信息！');
  }
});

child.stderr.on('data', (data) => {
  const text = data.toString();
  process.stderr.write(text);
});

// 等待CLI启动
setTimeout(() => {
  console.log('\n📝 发送测试消息...');
  child.stdin.write('你好，这是一个测试消息\n');
  
  // 等待响应后退出
  setTimeout(() => {
    child.stdin.write('exit\n');
    
    setTimeout(() => {
      if (hasSeenLLMInput) {
        console.log('\n🎉 测试成功！调试输出功能正常工作。');
        process.exit(0);
      } else {
        console.log('\n❌ 测试失败：未检测到LLM完整输入调试信息。');
        console.log('\n完整输出：');
        console.log(output);
        process.exit(1);
      }
    }, 2000);
  }, 5000);
}, 2000);

// 处理进程退出
child.on('close', (code) => {
  console.log(`\n进程退出，代码: ${code}`);
});

child.on('error', (error) => {
  console.error('\n❌ 进程错误:', error);
  process.exit(1);
});

// 超时保护
setTimeout(() => {
  console.log('\n⏰ 测试超时，强制退出');
  child.kill();
  process.exit(1);
}, 30000);