#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 模拟 processSingleFileContent 函数
async function processSingleFileContent(filePath, rootDirectory, offset, limit) {
  try {
    console.log(`Checking file existence: ${filePath}`);
    if (!fs.existsSync(filePath)) {
      console.log('File does not exist according to fs.existsSync');
      return {
        llmContent: '',
        returnDisplay: 'File not found.',
        error: `File not found: ${filePath}`,
      };
    }
    
    console.log('File exists, getting stats...');
    const stats = await fs.promises.stat(filePath);
    if (stats.isDirectory()) {
      return {
        llmContent: '',
        returnDisplay: 'Path is a directory.',
        error: `Path is a directory, not a file: ${filePath}`,
      };
    }

    console.log('File is valid, reading content...');
    const content = await fs.promises.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    return {
      llmContent: content.substring(0, 200) + '...',
      returnDisplay: `Read ${lines.length} lines from file`,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error in processSingleFileContent:', errorMessage);
    return {
      llmContent: `Error reading file: ${errorMessage}`,
      returnDisplay: `Error reading file: ${errorMessage}`,
      error: `Error reading file ${filePath}: ${errorMessage}`,
    };
  }
}

// 测试
const workingDir = process.cwd();
const filePath = path.resolve(workingDir, 'LICENSE');

console.log('Testing processSingleFileContent...');
console.log('File path:', filePath);
console.log('Root directory:', workingDir);

processSingleFileContent(filePath, workingDir)
  .then(result => {
    console.log('Result:', result);
  })
  .catch(error => {
    console.error('Unexpected error:', error);
  });