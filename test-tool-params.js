#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 模拟完整的工具参数处理流程
function hackReadFile(args, { workingDir }) {
  console.log('hackReadFile input:', args);
  console.log('workingDir:', workingDir);
  
  if (args && typeof args === 'object' && ('input' in args || 'absolute_path' in args)) {
    const inputValue = args.input || args.absolute_path;
    console.log('inputValue:', inputValue, 'type:', typeof inputValue);
    
    if (typeof inputValue === 'string') {
      if (!path.isAbsolute(inputValue)) {
        const absolutePath = path.resolve(workingDir, inputValue);
        console.log('Resolved relative path:', inputValue, '->', absolutePath);
        args.absolute_path = absolutePath;
      } else {
        console.log('Path is already absolute:', inputValue);
        args.absolute_path = inputValue;
      }
    } else {
      console.log('inputValue is not a string, using as-is');
      args.absolute_path = inputValue;
    }
    
    // Remove the input property to avoid confusion
    if ('input' in args) {
      delete args.input;
    }
  }
  
  console.log('hackReadFile output:', args);
  return args;
}

// 模拟 isWithinRoot 检查
function isWithinRoot(pathToCheck, rootDirectory) {
  const normalizedPathToCheck = path.resolve(pathToCheck);
  const normalizedRootDirectory = path.resolve(rootDirectory);

  const rootWithSeparator =
    normalizedRootDirectory === path.sep ||
    normalizedRootDirectory.endsWith(path.sep)
      ? normalizedRootDirectory
      : normalizedRootDirectory + path.sep;

  const result = (
    normalizedPathToCheck === normalizedRootDirectory ||
    normalizedPathToCheck.startsWith(rootWithSeparator)
  );
  
  console.log('isWithinRoot check:');
  console.log('  pathToCheck:', pathToCheck);
  console.log('  normalizedPathToCheck:', normalizedPathToCheck);
  console.log('  rootDirectory:', rootDirectory);
  console.log('  normalizedRootDirectory:', normalizedRootDirectory);
  console.log('  rootWithSeparator:', rootWithSeparator);
  console.log('  result:', result);
  
  return result;
}

// 模拟 processSingleFileContent
async function processSingleFileContent(filePath, rootDirectory) {
  console.log('\n=== processSingleFileContent ===');
  console.log('filePath:', filePath);
  console.log('rootDirectory:', rootDirectory);
  
  try {
    console.log('Checking file existence with fs.existsSync...');
    const exists = fs.existsSync(filePath);
    console.log('fs.existsSync result:', exists);
    
    if (!exists) {
      console.log('File does not exist according to fs.existsSync');
      return {
        llmContent: '',
        returnDisplay: 'File not found.',
        error: `File not found: ${filePath}`,
      };
    }
    
    console.log('File exists, getting stats...');
    const stats = await fs.promises.stat(filePath);
    console.log('File stats:', {
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      size: stats.size
    });
    
    if (stats.isDirectory()) {
      return {
        llmContent: '',
        returnDisplay: 'Path is a directory.',
        error: `Path is a directory, not a file: ${filePath}`,
      };
    }

    console.log('Reading file content...');
    const content = await fs.promises.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    return {
      llmContent: content.substring(0, 200) + '...',
      returnDisplay: `Read ${lines.length} lines from file`,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('Error in processSingleFileContent:', errorMessage);
    return {
      llmContent: `Error reading file: ${errorMessage}`,
      returnDisplay: `Error reading file: ${errorMessage}`,
      error: `Error reading file ${filePath}: ${errorMessage}`,
    };
  }
}

// 测试完整流程
async function testFullFlow() {
  console.log('=== Testing Full Tool Flow ===');
  
  const workingDir = process.cwd();
  const originalArgs = { input: 'LICENSE' };
  
  console.log('1. Original args:', originalArgs);
  
  // Step 1: hackToolParams
  const hackedArgs = hackReadFile({ ...originalArgs }, { workingDir });
  console.log('2. After hackToolParams:', hackedArgs);
  
  // Step 2: Validation (isWithinRoot check)
  const targetDir = workingDir; // config.getTargetDir()
  const withinRoot = isWithinRoot(hackedArgs.absolute_path, targetDir);
  
  if (!withinRoot) {
    console.log('❌ Validation failed: path not within root');
    return;
  }
  
  console.log('✅ Validation passed');
  
  // Step 3: Execute processSingleFileContent
  const result = await processSingleFileContent(hackedArgs.absolute_path, targetDir);
  console.log('3. processSingleFileContent result:', result);
  
  // Step 4: Format result (returnDisplay)
  const formattedResult = result.returnDisplay;
  console.log('4. Formatted result:', formattedResult);
}

testFullFlow().catch(console.error);