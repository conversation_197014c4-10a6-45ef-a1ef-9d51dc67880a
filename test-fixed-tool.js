#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 模拟修复后的 hackReadFile 函数
function hackReadFile(args, { workingDir }) {
  console.log('[hackReadFile] Input args:', args);
  console.log('[hackReadFile] Working dir:', workingDir);
  
  if (args && typeof args === 'object' && ('input' in args || 'absolute_path' in args)) {
    const inputValue = args.input || args.absolute_path;
    if (typeof inputValue === 'string') {
      if (!path.isAbsolute(inputValue)) {
        const absolutePath = path.resolve(workingDir, inputValue);
        args.absolute_path = absolutePath;
        console.log('[hackReadFile] Converted relative path:', inputValue, '->', absolutePath);
      } else {
        // Special handling for paths that look like absolute paths but might be intended as relative
        const basename = path.basename(inputValue);
        const potentialRelativePath = path.resolve(workingDir, basename);
        
        // If the absolute path doesn't exist but a file with the same name exists in workingDir,
        // use the relative path instead
        if (!fs.existsSync(inputValue) && fs.existsSync(potentialRelativePath)) {
          console.log(`[hackReadFile] Converting potentially incorrect absolute path '${inputValue}' to '${potentialRelativePath}'`);
          args.absolute_path = potentialRelativePath;
        } else {
          console.log('[hackReadFile] Using absolute path as-is:', inputValue);
          args.absolute_path = inputValue;
        }
      }
    } else {
      args.absolute_path = inputValue;
    }
    // Remove the input property to avoid confusion
    if ('input' in args) {
      delete args.input;
    }
  }
  
  console.log('[hackReadFile] Output args:', args);
  return args;
}

// 模拟 processSingleFileContent
async function processSingleFileContent(filePath, rootDirectory) {
  console.log('[processSingleFileContent] Called with:', filePath);
  
  try {
    if (!fs.existsSync(filePath)) {
      console.log('[processSingleFileContent] File not found');
      return {
        llmContent: '',
        returnDisplay: 'File not found.',
        error: `File not found: ${filePath}`,
      };
    }
    
    const stats = await fs.promises.stat(filePath);
    if (stats.isDirectory()) {
      return {
        llmContent: '',
        returnDisplay: 'Path is a directory.',
        error: `Path is a directory, not a file: ${filePath}`,
      };
    }

    const content = await fs.promises.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    console.log('[processSingleFileContent] Successfully read file, lines:', lines.length);
    
    // Return first few lines for testing
    const firstLines = lines.slice(0, 10).join('\n');
    
    return {
      llmContent: firstLines,
      returnDisplay: `Successfully read ${lines.length} lines from LICENSE file. First 10 lines:\n\n${firstLines}`,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('[processSingleFileContent] Error:', errorMessage);
    return {
      llmContent: `Error reading file: ${errorMessage}`,
      returnDisplay: `Error reading file: ${errorMessage}`,
      error: `Error reading file ${filePath}: ${errorMessage}`,
    };
  }
}

// 测试不同的输入情况
async function testCases() {
  const workingDir = process.cwd();
  
  console.log('=== Test Case 1: Relative path ===');
  const case1 = { input: 'LICENSE' };
  const hacked1 = hackReadFile({ ...case1 }, { workingDir });
  const result1 = await processSingleFileContent(hacked1.absolute_path, workingDir);
  console.log('Result 1:', result1.returnDisplay);
  
  console.log('\n=== Test Case 2: Incorrect absolute path ===');
  const case2 = { absolute_path: '/LICENSE' };
  const hacked2 = hackReadFile({ ...case2 }, { workingDir });
  const result2 = await processSingleFileContent(hacked2.absolute_path, workingDir);
  console.log('Result 2:', result2.returnDisplay);
  
  console.log('\n=== Test Case 3: Another incorrect absolute path ===');
  const case3 = { input: '/path/to/project/LICENSE' };
  const hacked3 = hackReadFile({ ...case3 }, { workingDir });
  const result3 = await processSingleFileContent(hacked3.absolute_path, workingDir);
  console.log('Result 3:', result3.returnDisplay);
}

testCases().catch(console.error);