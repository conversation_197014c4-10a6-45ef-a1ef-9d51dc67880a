#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * Simple test script to demonstrate the debug logger functionality
 */

import { logger, setDebugMode } from './packages/lang/dist/utils/logger.js';

console.log('=== Testing Logger Debug Functionality ===\n');

console.log('1. Testing with debug mode DISABLED:');
setDebugMode(false);
logger.info('This info message should appear');
logger.success('This success message should appear');
logger.warning('This warning message should appear');
logger.error('This error message should appear');
logger.debug('This debug message should NOT appear');

console.log('\n2. Testing with debug mode ENABLED:');
setDebugMode(true);
logger.info('This info message should appear');
logger.success('This success message should appear');
logger.warning('This warning message should appear');
logger.error('This error message should appear');
logger.debug('This debug message SHOULD appear');

console.log('\n3. Testing debug with additional arguments:');
logger.debug('Debug with object:', { key: 'value', number: 42 });
logger.debug('Debug with multiple args:', 'string', 123, true);

console.log('\n=== Test Complete ===');