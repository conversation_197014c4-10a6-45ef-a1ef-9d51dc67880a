#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 测试修复后的 processSingleFileContent 函数
async function testProcessSingleFileContent() {
  const filePath = path.resolve(process.cwd(), 'LICENSE');
  const rootDirectory = process.cwd();
  
  console.log('Testing processSingleFileContent with:');
  console.log('  filePath:', filePath);
  console.log('  rootDirectory:', rootDirectory);
  
  // 模拟修复后的函数逻辑
  try {
    if (!fs.existsSync(filePath)) {
      return {
        llmContent: '',
        returnDisplay: 'File not found.',
        error: `File not found: ${filePath}`,
      };
    }
    
    const stats = await fs.promises.stat(filePath);
    if (stats.isDirectory()) {
      return {
        llmContent: '',
        returnDisplay: 'Path is a directory.',
        error: `Path is a directory, not a file: ${filePath}`,
      };
    }

    const content = await fs.promises.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    const originalLineCount = lines.length;

    // 模拟默认限制
    const DEFAULT_MAX_LINES_TEXT_FILE = 1000;
    const MAX_LINE_LENGTH_TEXT_FILE = 2000;
    
    const startLine = 0;
    const effectiveLimit = DEFAULT_MAX_LINES_TEXT_FILE;
    const endLine = Math.min(startLine + effectiveLimit, originalLineCount);
    const actualStartLine = Math.min(startLine, originalLineCount);
    const selectedLines = lines.slice(actualStartLine, endLine);

    let linesWereTruncatedInLength = false;
    const formattedLines = selectedLines.map((line) => {
      if (line.length > MAX_LINE_LENGTH_TEXT_FILE) {
        linesWereTruncatedInLength = true;
        return (
          line.substring(0, MAX_LINE_LENGTH_TEXT_FILE) + '... [truncated]'
        );
      }
      return line;
    });

    const contentRangeTruncated = endLine < originalLineCount;
    const isTruncated = contentRangeTruncated || linesWereTruncatedInLength;

    let llmTextContent = '';
    if (contentRangeTruncated) {
      llmTextContent += `[File content truncated: showing lines ${actualStartLine + 1}-${endLine} of ${originalLineCount} total lines. Use offset/limit parameters to view more.]\n`;
    } else if (linesWereTruncatedInLength) {
      llmTextContent += `[File content partially truncated: some lines exceeded maximum length of ${MAX_LINE_LENGTH_TEXT_FILE} characters.]\n`;
    }
    llmTextContent += formattedLines.join('\n');

    // 修复后的 returnDisplay 逻辑
    const relativePathForDisplay = path
      .relative(rootDirectory, filePath)
      .replace(/\\/g, '/');
    
    let displayMessage = `Read ${originalLineCount} lines from ${relativePathForDisplay}`;
    if (isTruncated) {
      displayMessage += ' (truncated)';
    }

    return {
      llmContent: llmTextContent,
      returnDisplay: displayMessage,
      isTruncated,
      originalLineCount,
      linesShown: [actualStartLine + 1, endLine],
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const displayPath = path
      .relative(rootDirectory, filePath)
      .replace(/\\/g, '/');
    return {
      llmContent: `Error reading file ${displayPath}: ${errorMessage}`,
      returnDisplay: `Error reading file ${displayPath}: ${errorMessage}`,
      error: `Error reading file ${filePath}: ${errorMessage}`,
    };
  }
}

testProcessSingleFileContent()
  .then(result => {
    console.log('\nResult:');
    console.log('  returnDisplay:', result.returnDisplay);
    console.log('  llmContent length:', result.llmContent.length);
    console.log('  llmContent preview:', result.llmContent.substring(0, 200) + '...');
    console.log('  error:', result.error || 'none');
  })
  .catch(console.error);