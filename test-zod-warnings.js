#!/usr/bin/env node

/**
 * 测试Zod警告抑制功能
 */

import { createLangChainGeminiCLI } from './dist/index.js';

async function testZodWarnings() {
  console.log('🧪 测试Zod警告抑制...\n');
  
  // 捕获console.warn输出
  const warnings = [];
  const originalWarn = console.warn;
  console.warn = (...args) => {
    warnings.push(args.join(' '));
    originalWarn(...args);
  };
  
  try {
    // 创建CLI实例 - 这会触发工具绑定
    const cli = await createLangChainGeminiCLI({
      sessionId: 'zod-test-session',
      model: 'ht::saas-deepseek-v3',
      authType: 'USE_OPENAI_COMPATIBLE',
      targetDir: process.cwd(),
      cwd: process.cwd(),
      debugMode: false, // 关闭debug模式减少输出
      coreTools: ['list_directory', 'read_file'],
    });

    console.log('✅ CLI实例创建成功');
    
    // 恢复原始console.warn
    console.warn = originalWarn;
    
    // 检查是否有Zod警告
    const zodWarnings = warnings.filter(warning => 
      warning.includes('Zod field') && 
      warning.includes('.optional()') && 
      warning.includes('without .nullable()')
    );
    
    console.log(`\n📊 警告统计:`);
    console.log(`总警告数: ${warnings.length}`);
    console.log(`Zod相关警告: ${zodWarnings.length}`);
    
    if (zodWarnings.length > 0) {
      console.log('\n❌ Zod警告仍然存在:');
      zodWarnings.forEach((warning, index) => {
        console.log(`${index + 1}. ${warning.substring(0, 100)}...`);
      });
    } else {
      console.log('\n✅ Zod警告已成功抑制！');
    }
    
    // 测试工具描述
    const tools = cli.config.toolRegistry.getLangChainTools();
    console.log(`\n🔧 已注册工具数量: ${tools.length}`);
    
    if (tools.length > 0) {
      const firstTool = tools[0];
      console.log(`\n📝 第一个工具描述预览:`);
      console.log(`工具名: ${firstTool.name}`);
      console.log(`描述长度: ${firstTool.description.length} 字符`);
      console.log(`包含参数信息: ${firstTool.description.includes('**Parameters:**') ? '✅' : '❌'}`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

testZodWarnings().catch(console.error);
