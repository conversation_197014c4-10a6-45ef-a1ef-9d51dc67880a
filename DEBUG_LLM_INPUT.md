# LLM 调试输入功能

## 功能说明

当用户执行 `node packages/lang/dist/cli.js --interactive --debug` 命令时，系统会在调试模式下打印出调用LLM的完整输入。

## 实现位置

修改文件：`packages/lang/src/core/agent.ts`

在 `prepareMessages` 方法中添加了调试输出逻辑：

```typescript
// Debug mode: print complete LLM input
if (this.config.getDebugMode()) {
  logger.debug('\n=== LLM 完整输入 ===');
  preparedMessages.forEach((message, index) => {
    const messageType = message.constructor.name;
    const content = typeof message.content === 'string' ? message.content : JSON.stringify(message.content);
    logger.debug(`\n[${index}] ${messageType}:`);
    logger.debug(content);
    logger.debug('---');
  });
  logger.debug('=== LLM 输入结束 ===\n');
}
```

## 使用方法

### 1. 交互模式 + 调试
```bash
node packages/lang/dist/cli.js --interactive --debug
```

### 2. 单次命令 + 调试
```bash
node packages/lang/dist/cli.js --debug "你的问题"
```

## 输出示例

当启用调试模式时，会看到类似以下的输出：

```
=== LLM 完整输入 ===

[0] SystemMessage:
You are an interactive CLI agent specializing in software engineering tasks...
[完整的系统提示词内容]

[1] HumanMessage:
你好，这是测试
---
=== LLM 输入结束 ===
```

## 输出内容包括

1. **SystemMessage**: 完整的系统提示词，包括：
   - 核心指令 (Core Mandates)
   - 主要工作流程 (Primary Workflows)
   - 操作指南 (Operational Guidelines)
   - 可用工具列表 (Available Tools)

2. **HumanMessage**: 用户输入的消息

3. **其他消息**: 如果有工具调用或历史对话，也会显示相应的消息

## 测试验证

可以运行以下测试脚本验证功能：

```bash
node test-debug-output.js
```

或者直接测试：

```bash
node packages/lang/dist/cli.js --debug "测试消息"
```

## 注意事项

- 调试输出只在 `--debug` 模式下显示
- 输出内容可能很长，包含完整的系统提示词
- 调试信息通过 `logger.debug()` 输出，使用灰色文字显示