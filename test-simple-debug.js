#!/usr/bin/env node

/**
 * 简单测试：验证debug模式下的LLM输入打印功能
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 简单测试：验证debug模式下的LLM输入打印功能\n');

// 设置测试环境变量
const env = {
  ...process.env,
  OPENAI_API_KEY: 'sk-cITab2RRLmsfbUH-pNJg',
  OPENAI_BASE_URL: 'http://*************/web/unauth/LLM_api_proxy/v1',
  HAI_CODE_MODEL: 'ht::saas-deepseek-v3'
};

// 启动CLI进程
const cliPath = path.join(__dirname, 'packages/lang/dist/cli.js');
const child = spawn('node', [cliPath, '--debug', '你好'], {
  env,
  stdio: ['pipe', 'pipe', 'pipe']
});

let output = '';
let hasSeenLLMInput = false;

// 监听输出
child.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  
  // 检查是否看到了LLM输入调试信息
  if (text.includes('=== LLM 完整输入 ===')) {
    hasSeenLLMInput = true;
  }
});

child.stderr.on('data', (data) => {
  const text = data.toString();
  output += text;
});

// 处理进程退出
child.on('close', (code) => {
  if (hasSeenLLMInput) {
    console.log('✅ 测试成功！在debug模式下成功打印了LLM的完整输入。');
    console.log('\n关键输出片段：');
    const lines = output.split('\n');
    let inLLMSection = false;
    let sectionLines = [];
    
    for (const line of lines) {
      if (line.includes('=== LLM 完整输入 ===')) {
        inLLMSection = true;
        sectionLines.push(line);
      } else if (line.includes('=== LLM 输入结束 ===')) {
        sectionLines.push(line);
        break;
      } else if (inLLMSection && sectionLines.length < 10) {
        sectionLines.push(line);
      }
    }
    
    console.log(sectionLines.join('\n'));
    process.exit(0);
  } else {
    console.log('❌ 测试失败：未检测到LLM完整输入调试信息。');
    console.log('\n完整输出：');
    console.log(output);
    process.exit(1);
  }
});

child.on('error', (error) => {
  console.error('❌ 进程错误:', error);
  process.exit(1);
});

// 超时保护
setTimeout(() => {
  console.log('⏰ 测试超时，强制退出');
  child.kill();
  process.exit(1);
}, 15000);