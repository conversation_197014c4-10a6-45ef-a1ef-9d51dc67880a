#!/usr/bin/env node

/**
 * 测试第一次调用时是否包含工具参数信息
 */

import { spawn } from 'child_process';

console.log('测试第一次调用时的工具参数信息...\n');

const child = spawn('node', ['packages/lang/dist/cli.js', '--debug', '请使用list_directory工具'], {
  stdio: 'pipe',
  env: process.env
});

let output = '';
let foundToolParams = false;
let foundFirstSystemMessage = false;

child.stdout.on('data', (data) => {
  const text = data.toString();
  output += text;
  
  // 检查是否是第一个SystemMessage
  if (text.includes('[0] SystemMessage:') && !foundFirstSystemMessage) {
    foundFirstSystemMessage = true;
    console.log('✅ 发现第一个SystemMessage');
  }
  
  // 在第一个SystemMessage中检查是否包含工具参数信息
  if (foundFirstSystemMessage && !foundToolParams) {
    if (text.includes('**Parameters**:') && text.includes('- `path` (required)')) {
      foundToolParams = true;
      console.log('✅ 第一次调用就包含了工具参数信息！');
    }
  }
});

child.stderr.on('data', (data) => {
  output += data.toString();
});

child.on('close', (code) => {
  if (foundToolParams) {
    console.log('\n✅ 测试通过：第一次调用就包含了完整的工具参数信息');
  } else {
    console.log('\n❌ 测试失败：第一次调用未包含工具参数信息');
    console.log('\n部分输出内容：');
    console.log(output.substring(0, 2000) + '...');
  }
  
  process.exit(foundToolParams ? 0 : 1);
});

// 10秒后强制退出
setTimeout(() => {
  child.kill();
  if (foundToolParams) {
    console.log('\n✅ 测试通过：第一次调用就包含了完整的工具参数信息');
    process.exit(0);
  } else {
    console.log('\n❌ 测试超时：未能在10秒内检测到工具参数信息');
    process.exit(1);
  }
}, 10000);