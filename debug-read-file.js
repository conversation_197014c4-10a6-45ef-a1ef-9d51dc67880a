#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

import fs from 'fs';
import path from 'path';

// 模拟问题场景
const workingDir = process.cwd();
const inputPath = 'LICENSE';
const absolutePath = path.resolve(workingDir, inputPath);

console.log('=== Basic Path Resolution ===');
console.log('Working directory:', workingDir);
console.log('Input path:', inputPath);
console.log('Resolved absolute path:', absolutePath);
console.log('File exists (fs.existsSync):', fs.existsSync(absolutePath));

// 尝试读取文件
try {
  const stats = fs.statSync(absolutePath);
  console.log('File stats:', {
    isFile: stats.isFile(),
    isDirectory: stats.isDirectory(),
    size: stats.size
  });
  
  const content = fs.readFileSync(absolutePath, 'utf8');
  console.log('File content (first 100 chars):', content.substring(0, 100));
} catch (error) {
  console.error('Error reading file:', error.message);
}

// 模拟 isWithinRoot 检查
function isWithinRoot(pathToCheck, rootDirectory) {
  const normalizedPathToCheck = path.resolve(pathToCheck);
  const normalizedRootDirectory = path.resolve(rootDirectory);

  const rootWithSeparator =
    normalizedRootDirectory === path.sep ||
    normalizedRootDirectory.endsWith(path.sep)
      ? normalizedRootDirectory
      : normalizedRootDirectory + path.sep;

  return (
    normalizedPathToCheck === normalizedRootDirectory ||
    normalizedPathToCheck.startsWith(rootWithSeparator)
  );
}

console.log('\n=== isWithinRoot Check ===');
const targetDir = workingDir; // 模拟 config.getTargetDir()
console.log('Target directory:', targetDir);
console.log('Is within root:', isWithinRoot(absolutePath, targetDir));

// 模拟 hackToolParams 处理
console.log('\n=== hackToolParams Simulation ===');
const originalArgs = { input: inputPath };
console.log('Original args:', originalArgs);

// 模拟 hackReadFile 函数
function hackReadFile(args, { workingDir }) {
  if (args && typeof args === 'object' && ('input' in args || 'absolute_path' in args)) {
    const inputValue = args.input || args.absolute_path;
    if (typeof inputValue === 'string') {
      if (!path.isAbsolute(inputValue)) {
        const absolutePath = path.resolve(workingDir, inputValue);
        args.absolute_path = absolutePath;
      } else {
        args.absolute_path = inputValue;
      }
    } else {
      args.absolute_path = inputValue;
    }
    // Remove the input property to avoid confusion
    if ('input' in args) {
      delete args.input;
    }
  }
  return args;
}

const hackedArgs = hackReadFile({ ...originalArgs }, { workingDir });
console.log('Hacked args:', hackedArgs);