#!/usr/bin/env node

/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */

/**
 * 测试debug模式下工具调用结果的打印功能
 */

import { createLangChainGeminiCLI } from './packages/lang/dist/index.js';

async function testDebugToolCalls() {
  console.log('=== 测试debug模式下的工具调用输出 ===\n');

  try {
    // 创建CLI实例，启用debug模式
    const cli = await createLangChainGeminiCLI({
      sessionId: `debug-test-${Date.now()}`,
      model: 'ht::saas-deepseek-v3',
      targetDir: process.cwd(),
      debugMode: true, // 启用debug模式
      question: '',
      fullContext: false,
      userMemory: '',
      geminiMdFileCount: 50,
      cwd: process.cwd(),
      authType: 'ht',
      telemetry: {
        enabled: false,
      },
    });

    console.log('✅ CLI实例创建成功，debug模式已启用\n');

    // 测试一个会触发工具调用的问题
    const testMessage = '请列出当前目录下的文件';
    
    console.log(`📝 测试消息: ${testMessage}\n`);
    console.log('🔍 开始处理消息，观察debug输出:\n');

    // 使用流式处理来观察debug输出
    const stream = cli.streamMessage(testMessage);
    
    let responseContent = '';
    for await (const chunk of stream) {
      responseContent += chunk;
      process.stdout.write(chunk);
    }

    console.log('\n\n✅ 测试完成');
    console.log('📊 响应长度:', responseContent.length, '字符');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// 运行测试
testDebugToolCalls().catch(console.error);