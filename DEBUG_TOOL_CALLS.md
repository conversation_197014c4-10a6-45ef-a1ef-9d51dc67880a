# Debug模式下的工具调用输出功能

## 概述

在debug模式下，系统现在会详细输出工具调用的过程，包括：
- 工具名称
- 输入参数
- 执行状态
- 输出结果

这个功能有助于开发者和用户了解AI助手在执行任务时具体调用了哪些工具，以及工具的执行情况，便于问题定位和调试。

## 使用方法

### 启用debug模式

使用 `--debug` 或 `-d` 参数启用debug模式：

```bash
# 非交互模式
node packages/lang/dist/cli.js --debug --prompt "你的问题"

# 交互模式
node packages/lang/dist/cli.js --debug --interactive
```

### Debug输出格式

当AI助手调用工具时，你会看到以下格式的debug输出：

```
[工具调用] 🔧 执行工具: tool_name
[工具调用] 📥 输入参数: {
  "param1": "value1",
  "param2": "value2"
}
[工具调用] ✅ 工具执行成功: tool_name
[工具调用] 📤 输出结果: 工具的执行结果...
```

如果工具执行失败，会显示：

```
[工具调用] ❌ 工具执行失败: tool_name
[工具调用] 💥 错误信息: 具体的错误信息
```

## 示例

### 示例1：目录列表工具

```bash
$ node packages/lang/dist/cli.js --debug --prompt "请列出当前目录下的文件"

[工具调用] 🔧 执行工具: list_directory
[工具调用] 📥 输入参数: {
  "input": "."
}
[工具调用] ✅ 工具执行成功: list_directory
[工具调用] 📤 输出结果: Listed 57 item(s). (4 git-ignored)
```

### 示例2：文件搜索工具

```bash
$ node packages/lang/dist/cli.js --debug --prompt "请搜索包含'logger'的文件"

[工具调用] 🔧 执行工具: glob
[工具调用] 📥 输入参数: {
  "pattern": "**/*logger*"
}
[工具调用] ✅ 工具执行成功: glob
[工具调用] 📤 输出结果: Found 12 matching file(s)
```

### 示例3：文件内容搜索工具

```bash
$ node packages/lang/dist/cli.js --debug --prompt "请搜索包含'debug'的代码"

[工具调用] 🔧 执行工具: search_file_content
[工具调用] 📥 输入参数: {
  "pattern": "debug"
}
[工具调用] ✅ 工具执行成功: search_file_content
[工具调用] 📤 输出结果: Found 911 matches
```

## 技术实现

### LangChainAgent

在 `packages/lang/src/core/agent.ts` 中的 `executeToolCalls` 方法中添加了debug输出：

```typescript
private async executeToolCalls(toolCalls: ToolCall[]): Promise<ToolMessage[]> {
  // ... 其他代码

  for (const toolCall of toolCalls) {
    // ... 工具查找逻辑

    try {
      if (this.config.getDebugMode()) {
        logger.debug(`[工具调用] 🔧 执行工具: ${toolCall.name}`);
        logger.debug(`[工具调用] 📥 输入参数: ${JSON.stringify(toolCall.args, null, 2)}`);
      }

      const result = await tool.invoke(toolCall.args || {});
      
      if (this.config.getDebugMode()) {
        logger.debug(`[工具调用] ✅ 工具执行成功: ${toolCall.name}`);
        logger.debug(`[工具调用] 📤 输出结果: ${result.substring(0, 500)}...`);
      }

      // ... 处理结果
    } catch (toolError) {
      if (this.config.getDebugMode()) {
        logger.debug(`[工具调用] ❌ 工具执行失败: ${toolCall.name}`);
        logger.debug(`[工具调用] 💥 错误信息: ${errorMsg}`);
      }
      // ... 错误处理
    }
  }
}
```

### StateGraphAgent

在 `packages/lang/src/core/stateGraphAgent.ts` 中为LangGraph的ToolNode添加了debug包装器：

```typescript
private createDebugToolWrapper(toolNode: any) {
  return async (state: AgentStateType): Promise<Partial<AgentStateType>> => {
    // 在工具执行前记录debug信息
    if (this.config.getDebugMode()) {
      // 记录工具调用信息
    }
    
    // 执行原始工具节点
    const result = await toolNode(state);
    
    // 在工具执行后记录debug信息
    if (this.config.getDebugMode()) {
      // 记录工具执行结果
    }
    
    return result;
  };
}
```

## 配置

Debug模式通过以下方式启用：

1. **CLI参数**: `--debug` 或 `-d`
2. **配置对象**: `debugMode: true`
3. **全局logger**: 通过 `setDebugMode(true)` 设置

当debug模式启用时，`logger.debug()` 调用会输出到控制台，否则会被忽略。

## 输出控制

- **长输出截断**: 工具输出结果超过500字符时会被截断，并显示"...(截断)"
- **JSON格式化**: 输入参数以格式化的JSON形式显示，便于阅读
- **彩色输出**: 使用不同的emoji和颜色来区分不同类型的信息

## 注意事项

1. Debug输出会增加日志量，建议仅在调试时使用
2. 敏感信息（如API密钥）不会在debug输出中显示
3. 工具输出结果会被截断以避免过长的日志
4. Debug模式不影响AI助手的正常功能，只是增加了额外的日志输出

## 测试

可以使用以下测试脚本验证debug功能：

```bash
# 运行完整测试
node test-debug-complete.js

# 或者手动测试
node packages/lang/dist/cli.js --debug --prompt "请列出当前目录下的文件"
```

这个功能大大提升了系统的可观测性，让用户能够清楚地了解AI助手在后台执行的具体操作。