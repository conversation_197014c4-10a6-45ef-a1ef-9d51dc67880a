#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// 模拟 Config 类
class MockConfig {
  constructor() {
    this.targetDir = process.cwd();
  }
  
  getTargetDir() {
    return this.targetDir;
  }
  
  getFileService() {
    return {
      shouldGeminiIgnoreFile: () => false
    };
  }
}

// 模拟 isWithinRoot 函数
function isWithinRoot(pathToCheck, rootDirectory) {
  const normalizedPathToCheck = path.resolve(pathToCheck);
  const normalizedRootDirectory = path.resolve(rootDirectory);

  const rootWithSeparator =
    normalizedRootDirectory === path.sep ||
    normalizedRootDirectory.endsWith(path.sep)
      ? normalizedRootDirectory
      : normalizedRootDirectory + path.sep;

  return (
    normalizedPathToCheck === normalizedRootDirectory ||
    normalizedPathToCheck.startsWith(rootWithSeparator)
  );
}

// 模拟 processSingleFileContent 函数
async function processSingleFileContent(filePath, rootDirectory, offset, limit) {
  console.log(`[processSingleFileContent] Called with:`);
  console.log(`  filePath: ${filePath}`);
  console.log(`  rootDirectory: ${rootDirectory}`);
  console.log(`  offset: ${offset}`);
  console.log(`  limit: ${limit}`);
  
  try {
    console.log(`[processSingleFileContent] Checking file existence...`);
    const exists = fs.existsSync(filePath);
    console.log(`[processSingleFileContent] fs.existsSync result: ${exists}`);
    
    if (!exists) {
      console.log(`[processSingleFileContent] File not found, returning error`);
      return {
        llmContent: '',
        returnDisplay: 'File not found.',
        error: `File not found: ${filePath}`,
      };
    }
    
    console.log(`[processSingleFileContent] Getting file stats...`);
    const stats = await fs.promises.stat(filePath);
    console.log(`[processSingleFileContent] File stats:`, {
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      size: stats.size
    });
    
    if (stats.isDirectory()) {
      return {
        llmContent: '',
        returnDisplay: 'Path is a directory.',
        error: `Path is a directory, not a file: ${filePath}`,
      };
    }

    console.log(`[processSingleFileContent] Reading file content...`);
    const content = await fs.promises.readFile(filePath, 'utf8');
    const lines = content.split('\n');
    
    console.log(`[processSingleFileContent] Successfully read ${lines.length} lines`);
    
    return {
      llmContent: content.substring(0, 200) + '...',
      returnDisplay: `Read ${lines.length} lines from file`,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[processSingleFileContent] Error:`, errorMessage);
    return {
      llmContent: `Error reading file: ${errorMessage}`,
      returnDisplay: `Error reading file: ${errorMessage}`,
      error: `Error reading file ${filePath}: ${errorMessage}`,
    };
  }
}

// 模拟 ReadFileTool 的 validateToolParams 方法
function validateToolParams(params, config) {
  console.log(`[validateToolParams] Called with:`, params);
  
  const filePath = params.absolute_path;
  if (!path.isAbsolute(filePath)) {
    const error = `File path must be absolute, but was relative: ${filePath}. You must provide an absolute path.`;
    console.log(`[validateToolParams] Error: ${error}`);
    return error;
  }
  
  if (!isWithinRoot(filePath, config.getTargetDir())) {
    const error = `File path must be within the root directory (${config.getTargetDir()}): ${filePath}`;
    console.log(`[validateToolParams] Error: ${error}`);
    return error;
  }
  
  if (params.offset !== undefined && params.offset < 0) {
    const error = 'Offset must be a non-negative number';
    console.log(`[validateToolParams] Error: ${error}`);
    return error;
  }
  
  if (params.limit !== undefined && params.limit <= 0) {
    const error = 'Limit must be a positive number';
    console.log(`[validateToolParams] Error: ${error}`);
    return error;
  }

  const fileService = config.getFileService();
  if (fileService.shouldGeminiIgnoreFile(params.absolute_path)) {
    const error = `File path '${filePath}' is ignored by .geminiignore pattern(s).`;
    console.log(`[validateToolParams] Error: ${error}`);
    return error;
  }

  console.log(`[validateToolParams] Validation passed`);
  return null;
}

// 模拟 ReadFileTool 的 execute 方法
async function executeReadFileTool(params, config) {
  console.log(`[executeReadFileTool] Starting execution with params:`, params);
  
  const validationError = validateToolParams(params, config);
  if (validationError) {
    console.log(`[executeReadFileTool] Validation failed: ${validationError}`);
    return {
      llmContent: `Error: Invalid parameters provided. Reason: ${validationError}`,
      returnDisplay: validationError,
    };
  }

  console.log(`[executeReadFileTool] Calling processSingleFileContent...`);
  const result = await processSingleFileContent(
    params.absolute_path,
    config.getTargetDir(),
    params.offset,
    params.limit,
  );

  console.log(`[executeReadFileTool] processSingleFileContent result:`, result);

  if (result.error) {
    console.log(`[executeReadFileTool] Returning error result`);
    return {
      llmContent: result.error, // The detailed error for LLM
      returnDisplay: result.returnDisplay, // User-friendly error
    };
  }

  console.log(`[executeReadFileTool] Returning success result`);
  return {
    llmContent: result.llmContent,
    returnDisplay: result.returnDisplay,
  };
}

// 测试完整的 ReadFileTool 执行流程
async function testReadFileTool() {
  console.log('=== Testing ReadFileTool Execution ===');
  
  const config = new MockConfig();
  const params = {
    absolute_path: path.resolve(process.cwd(), 'LICENSE')
  };
  
  console.log('Config target dir:', config.getTargetDir());
  console.log('Test params:', params);
  
  const result = await executeReadFileTool(params, config);
  console.log('Final result:', result);
  
  // 模拟 formatToolResult
  const formattedResult = result.returnDisplay;
  console.log('Formatted result (what LLM sees):', formattedResult);
}

testReadFileTool().catch(console.error);